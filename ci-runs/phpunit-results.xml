<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="/Users/<USER>/Projects/Clients/FPMP/smp_online/phpunit.xml" tests="18" assertions="94" errors="0" failures="0" skipped="0" time="0.179034">
    <testsuite name="Feature" tests="18" assertions="94" errors="0" failures="0" skipped="0" time="0.179034">
      <testsuite name="Tests\Feature\AuthenticationTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AuthenticationTest.php" tests="1" assertions="6" errors="0" failures="0" skipped="0" time="0.075194">
        <testcase name="test_role_based_dashboard_redirection" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AuthenticationTest.php" line="105" class="Tests\Feature\AuthenticationTest" classname="Tests.Feature.AuthenticationTest" assertions="6" time="0.075194"/>
      </testsuite>
      <testsuite name="Tests\Feature\ClientDashboardLayoutTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" tests="13" assertions="76" errors="0" failures="0" skipped="0" time="0.084475">
        <testcase name="client_dashboard_loads_successfully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="44" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="4" time="0.009918"/>
        <testcase name="client_dashboard_has_proper_layout_structure" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="56" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="12" time="0.006562"/>
        <testcase name="client_dashboard_shows_appropriate_buttons_with_proper_spacing" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="83" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="5" time="0.006351"/>
        <testcase name="client_header_dropdown_shows_appropriate_links" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="105" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="7" time="0.006982"/>
        <testcase name="normal_user_header_dropdown_shows_reservation_links" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="125" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="5" time="0.006743"/>
        <testcase name="admin_header_dropdown_shows_reservation_links" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="140" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="5" time="0.006912"/>
        <testcase name="client_dashboard_has_responsive_layout" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="155" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="6" time="0.005811"/>
        <testcase name="client_dashboard_quick_actions_are_properly_spaced" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="173" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="6" time="0.005510"/>
        <testcase name="client_dashboard_feature_cards_have_proper_button_layout" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="191" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="6" time="0.005898"/>
        <testcase name="client_dashboard_welcome_section_has_proper_icon_spacing" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="209" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="5" time="0.005337"/>
        <testcase name="client_dashboard_access_notice_is_informative" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="226" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="6" time="0.005400"/>
        <testcase name="client_dashboard_support_function_is_included" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="240" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="3" time="0.005705"/>
        <testcase name="client_dashboard_maintains_consistency_with_other_dashboards" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/ClientDashboardLayoutTest.php" line="255" class="Tests\Feature\ClientDashboardLayoutTest" classname="Tests.Feature.ClientDashboardLayoutTest" assertions="6" time="0.007345"/>
      </testsuite>
      <testsuite name="Tests\Feature\LogoutErrorFixTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" tests="4" assertions="12" errors="0" failures="0" skipped="0" time="0.019365">
        <testcase name="dashboard_pages_handle_null_user_gracefully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="104" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="3" time="0.006603"/>
        <testcase name="admin_dashboard_handles_null_user_gracefully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="115" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="3" time="0.004189"/>
        <testcase name="client_dashboard_handles_null_user_gracefully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="126" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="3" time="0.003694"/>
        <testcase name="main_dashboard_route_handles_null_user_gracefully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="137" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="3" time="0.004880"/>
      </testsuite>
    </testsuite>
  </testsuite>
</testsuites>
